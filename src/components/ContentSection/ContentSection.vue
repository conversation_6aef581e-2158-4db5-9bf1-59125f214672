<script setup lang="ts">
import type { CopywritingInfoVO, CopywritingVO, SaveCopywritingReq } from '@/api/types'
import { queryCopywriting, saveCopywriting } from '@/api/imageTranslationApi'
import AITranslateDialog from '@/components/AITranslateDialog/AITranslateDialog.vue'
import { Check, Clock, Picture } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'

const { t } = useI18n()
const route = useRoute()

// 判断当前页面
const isTranslatePage = computed(() => route.path === '/translate')
const isDesignPage = computed(() => route.path === '/design')
const isDesignCheckPage = computed(() => route.path === '/design-check')

// 加载状态
const loading = ref(false)

// 商品信息
const productInfo = ref({
  chineseName: '',
  japaneseName: '',
  link: '',
  nameResourceId: '', // 用于保存时的资源ID
})

// 字段翻译数据 - 用于翻译区域
const fieldTranslations = ref<{
  [fieldName: string]: {
    chinese: string
    japanese: string
    resourceIds: string[] // 存储所有相关的resourceId
  }
}>({})

// SKU数据 - 用于表格展示
const skuData = ref<{
  id: string
  image: string
  thumbnail: string
}[]>([])

// 表格展示数据 - 笛卡尔积
const tableDisplayData = ref<{
  fieldName: string
  chinese: string
  japanese: string
  skuResults: Array<{
    skuId: string
    image: string
    thumbnail: string
    resourceId?: string
  }>
}[]>([])

// 备注内容
const remark = ref('')

// 获取当前翻译ID
function getCurrentTransId() {
  const transId = route.query.transId as string
  return transId || ''
}

// 清空内容
function clearContent() {
  // 清空日文名称输入框
  productInfo.value.japaneseName = ''
  // 清空备注
  remark.value = ''
}

// AI翻译对话框相关状态
const showAIDialog = ref(false)
const currentTranslateText = ref('')

// 打开AI翻译对话框
function aiTranslate() {
  // 设置要翻译的内容为商品中文名
  currentTranslateText.value = productInfo.value.chineseName
  showAIDialog.value = true
}

// 处理AI翻译结果
function handleTranslateResult(result: string) {
  if (result) {
    productInfo.value.japaneseName = result
  }
  showAIDialog.value = false
}

// 保存文案信息
async function saveCopywritingData() {
  try {
    const saveRequests: SaveCopywritingReq[] = []

    // 添加商品名称的保存请求
    if (productInfo.value.nameResourceId) {
      saveRequests.push({
        resourceId: productInfo.value.nameResourceId,
        transValue: productInfo.value.japaneseName,
        transRemark: remark.value,
      })
    }

    // 添加字段翻译的保存请求
    Object.entries(fieldTranslations.value).forEach(([_fieldName, fieldData]) => {
      fieldData.resourceIds.forEach((resourceId) => {
        if (resourceId) {
          saveRequests.push({
            resourceId,
            transValue: fieldData.japanese,
            transRemark: '',
          })
        }
      })
    })

    if (saveRequests.length > 0) {
      const response = await saveCopywriting(saveRequests)
      if (response.success) {
        ElMessage.success(t('page.content.save_success'))
        // 保存成功后重新获取数据以更新显示
        await fetchCopywritingData()
      }
      else {
        ElMessage.error(response.message || t('page.content.save_failed'))
      }
    }
  }
  catch (error) {
    console.error('保存文案信息失败:', error)
    ElMessage.error(t('page.content.save_failed'))
  }
}

// 获取文案信息
async function fetchCopywritingData() {
  const transId = getCurrentTransId()
  if (!transId) {
    ElMessage.warning(t('page.content.no_trans_id'))
    return
  }

  loading.value = true
  try {
    const response = await queryCopywriting(transId)
    if (response.success) {
      if (!response.data)
        return
      processCopywritingData(response.data)
    }
    else {
      ElMessage.error(response.message || t('page.content.fetch_failed'))
    }
  }
  catch (error) {
    console.error('获取文案信息失败:', error)
    ElMessage.error(t('page.content.fetch_failed'))
  }
  finally {
    loading.value = false
  }
}

// 处理文案数据
function processCopywritingData(data: CopywritingInfoVO) {
  // 处理商品名称
  productInfo.value = {
    chineseName: data.spuName?.sourceLanguage || '',
    japaneseName: data.spuName?.targetLanguage || '',
    link: data.spuLink || '',
    nameResourceId: data.nameResourceId || '',
  }

  // 重置数据
  fieldTranslations.value = {}
  skuData.value = []
  tableDisplayData.value = []

  // 处理SKU信息
  if (data.skuInfo && data.skuInfo.length > 0) {
    // 提取SKU基本信息
    skuData.value = data.skuInfo.map(sku => ({
      id: sku.sku || '',
      image: sku.image || '',
      thumbnail: sku.thumbnail || '',
    }))

    // 提取字段翻译信息
    const fieldMap = new Map<string, {
      chinese: string
      japanese: string
      resourceIds: string[]
    }>()

    data.skuInfo.forEach((sku) => {
      sku.copywritingMap?.forEach((item: CopywritingVO) => {
        const fieldName = item.name || ''
        if (!fieldMap.has(fieldName)) {
          fieldMap.set(fieldName, {
            chinese: item.value?.sourceLanguage || '',
            japanese: item.value?.targetLanguage || '',
            resourceIds: [],
          })
        }
        if (item.resourceId) {
          fieldMap.get(fieldName)!.resourceIds.push(item.resourceId)
        }
      })
    })

    // 转换为响应式数据
    fieldTranslations.value = Object.fromEntries(fieldMap)

    // 生成表格显示数据（笛卡尔积）
    generateTableDisplayData()
  }
}

// 生成表格显示数据
function generateTableDisplayData() {
  tableDisplayData.value = Object.entries(fieldTranslations.value).map(([fieldName, fieldData]) => ({
    fieldName,
    chinese: fieldData.chinese,
    japanese: fieldData.japanese,
    skuResults: skuData.value.map((sku, index) => ({
      skuId: sku.id,
      image: sku.image,
      thumbnail: sku.thumbnail,
      resourceId: fieldData.resourceIds[index] || '', // 按顺序匹配resourceId
    })),
  }))
}

// 更新字段翻译
function updateFieldTranslation(fieldName: string, translation: string) {
  if (fieldTranslations.value[fieldName]) {
    fieldTranslations.value[fieldName].japanese = translation
    // 更新表格显示数据
    generateTableDisplayData()
  }
}

onMounted(() => {
  fetchCopywritingData()
})
</script>

<template>
  <div v-loading="loading" class="content-section">
    <div class="product-info-section">
      <div class="info-row three-columns">
        <div class="info-item">
          <div class="label">
            {{ $t('page.content.product_chinese_name') }}：
          </div>
          <div class="value">
            <div class="text-display">
              {{ productInfo.chineseName }}
            </div>
          </div>
        </div>
        <div class="info-item">
          <div class="label">
            {{ $t('page.content.product_japanese_name') }}：
          </div>
          <div class="value">
            <ElPopover
              :visible="showAIDialog"
              placement="bottom-start"
              hide-on-press-escape
              :trigger-keys="[]"
              :width="420"
            >
              <template #default>
                <AITranslateDialog
                  :model-value="showAIDialog"
                  :initial-text="productInfo.chineseName"
                  @translate-result="handleTranslateResult"
                  @close="showAIDialog = false"
                />
              </template>
              <template #reference>
                <el-input
                  v-if="isTranslatePage"
                  v-model="productInfo.japaneseName"
                  :placeholder="$t('page.content.enter_japanese_name')"
                />
                <div v-else class="text-display" :class="{ 'text-placeholder': !productInfo.japaneseName }">
                  {{ productInfo.japaneseName || $t('page.content.no_japanese_name') }}
                </div>
              </template>
            </ElPopover>
            <el-button v-if="isTranslatePage" type="primary" class="ai-button" @click="aiTranslate">
              AI
            </el-button>
            <el-button v-if="isTranslatePage" type="default" class="clear-button" @click="clearContent">
              {{ $t('page.content.clear') }}
            </el-button>
          </div>
        </div>
        <div class="info-item">
          <div class="label">
            {{ $t('page.content.remark') }}：
          </div>
          <div class="value">
            <el-input v-if="isTranslatePage" v-model="remark" :placeholder="$t('page.content.enter_remark')" />
            <div v-else class="text-display" :class="{ 'text-placeholder': !remark }">
              {{ remark || $t('page.content.no_remark') }}
            </div>
          </div>
        </div>
      </div>
      <!-- 商品链接 - 仅在设计和设计校验页面显示 -->
      <div v-if="isDesignPage || isDesignCheckPage" class="info-row">
        <div class="info-item full-width">
          <div class="label">
            {{ $t('page.content.product_link') }}：
          </div>
          <div class="value">
            <div class="text-display" :class="{ 'text-placeholder': !productInfo.link }">
              {{ productInfo.link || $t('page.content.no_link') }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 字段翻译区域 - 仅在翻译页面显示 -->
    <div v-if="isTranslatePage && Object.keys(fieldTranslations).length > 0" class="field-translation-section">
      <div class="section-title">
        {{ $t('page.content.field_translation') }}
      </div>
      <div class="field-list">
        <div v-for="(fieldData, fieldName) in fieldTranslations" :key="fieldName" class="field-item">
          <div class="field-info">
            <div class="field-label">
              {{ fieldName }}
            </div>
            <div class="field-chinese">
              {{ fieldData.chinese }}
            </div>
          </div>
          <div class="field-translation">
            <el-input
              v-model="fieldData.japanese"
              :placeholder="$t('page.content.enter_translation')"
              @input="updateFieldTranslation(fieldName as string, fieldData.japanese)"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 表格展示区域 -->
    <div v-if="tableDisplayData.length > 0" class="content-table-section">
      <div class="section-title">
        {{ $t('page.content.translation_results') }}
      </div>
      <div class="cartesian-table">
        <div class="table-header">
          <div class="header-cell field-name">
            字段名称
          </div>
          <div class="header-cell chinese-text">
            中文
          </div>
          <div class="header-cell japanese-text">
            日文
          </div>
          <div v-for="sku in skuData" :key="sku.id" class="header-cell sku-column">
            <div class="sku-info">
              <div class="sku-id">
                {{ sku.id }}
              </div>
              <div class="sku-image">
                <div v-if="sku.thumbnail" class="sku-thumbnail">
                  <img :src="sku.thumbnail" alt="SKU缩略图">
                </div>
                <div v-else class="image-placeholder">
                  <el-icon>
                    <Picture />
                  </el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-for="row in tableDisplayData" :key="row.fieldName" class="table-row">
          <div class="table-cell field-name">
            {{ row.fieldName }}
          </div>
          <div class="table-cell chinese-text">
            {{ row.chinese }}
          </div>
          <div class="table-cell japanese-text">
            <div class="text-display" :class="{ 'text-placeholder': !row.japanese }">
              {{ row.japanese || $t('page.content.no_translation') }}
            </div>
          </div>
          <div v-for="skuResult in row.skuResults" :key="skuResult.skuId" class="table-cell sku-column">
            <div class="sku-result">
              <el-icon v-if="row.japanese" class="success-icon" color="#67c23a">
                <Check />
              </el-icon>
              <el-icon v-else class="pending-icon" color="#e6a23c">
                <Clock />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 保存按钮 - 仅在翻译页面显示 -->
    <div v-if="isTranslatePage" class="action-buttons">
      <el-button type="primary" @click="saveCopywritingData">
        {{ $t('page.content.save') }}
      </el-button>
      <el-button type="default" @click="fetchCopywritingData">
        {{ $t('page.content.refresh') }}
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.content-section {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.product-info-section {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  .info-row {
    display: flex;
    margin-bottom: 16px;
    gap: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    &.three-columns {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
    }
  }

  .info-item {
    flex: 1;
    display: flex;

    &.full-width {
      width: 100%;
    }

    .label {
      min-width: 80px;
      flex-shrink: 0;
      line-height: 32px;
      font-weight: 500;
    }

    .value {
      flex: 1;
      display: flex;
      gap: 10px;

      .el-input {
        flex: 1;
      }
    }
  }

  .ai-button {
    background-color: #399e96;
    color: white;
  }

  .text-display {
    min-height: 32px;
    line-height: 32px;
    padding: 0 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    color: #606266;

    &.text-placeholder {
      color: #909399;
      font-style: italic;
    }
  }

  .source-lang-select {
    width: 100%;
  }
}

// 字段翻译区域样式
.field-translation-section {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  .section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #303133;
  }

  .field-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .field-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fafafa;

    .field-info {
      flex: 1;
      display: flex;
      gap: 20px;

      .field-label {
        min-width: 120px;
        font-weight: 500;
        color: #606266;
      }

      .field-chinese {
        flex: 1;
        color: #303133;
      }
    }

    .field-translation {
      flex: 1;

      .el-input {
        width: 100%;
      }
    }
  }
}

// 表格展示区域样式
.content-table-section {
  margin-bottom: 20px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #303133;
    background-color: #fff;
    padding: 16px 20px 0;
    border-radius: 4px 4px 0 0;
  }
}

.cartesian-table {
  background-color: #fff;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;

  .table-header {
    display: grid;
    grid-template-columns: 150px 200px 200px repeat(auto-fit, minmax(120px, 1fr));
    background-color: #f8f8f8;
    border-bottom: 2px solid #e0e0e0;
    font-weight: 600;
  }

  .table-row {
    display: grid;
    grid-template-columns: 150px 200px 200px repeat(auto-fit, minmax(120px, 1fr));
    border-bottom: 1px solid #e0e0e0;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f5f7fa;
    }
  }

  .header-cell,
  .table-cell {
    padding: 12px;
    border-right: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;

    &:last-child {
      border-right: none;
    }

    &.field-name {
      justify-content: flex-start;
      font-weight: 500;
      background-color: #fafbfc;
    }

    &.chinese-text,
    &.japanese-text {
      justify-content: flex-start;
      text-align: left;
    }

    &.sku-column {
      flex-direction: column;
      padding: 8px;
    }
  }

  .sku-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .sku-id {
      font-size: 12px;
      font-weight: 500;
      color: #606266;
    }

    .sku-image {
      .sku-thumbnail {
        width: 60px;
        height: 60px;
        border-radius: 4px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .image-placeholder {
        width: 60px;
        height: 60px;
        background-color: #f0f0f0;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 4px;

        .el-icon {
          font-size: 24px;
          color: #ccc;
        }
      }
    }
  }

  .sku-result {
    display: flex;
    justify-content: center;
    align-items: center;

    .success-icon,
    .pending-icon {
      font-size: 20px;
    }
  }

  .text-display {
    width: 100%;
    min-height: 32px;
    line-height: 32px;
    padding: 0 8px;
    background-color: #f5f7fa;
    border-radius: 4px;
    color: #606266;

    &.text-placeholder {
      color: #909399;
      font-style: italic;
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style>
